package pojo;

/**
 * Static registry of all route permissions in the system.
 * This class defines the permissions required for accessing different routes and functionalities.
 * Each permission is defined as a static Permission object with a unique code, name, and description.
 * 
 * <AUTHOR>
 */
public class RoutesPermission {

    // User Management Permission
    public static final Permission USER_MANAGEMENT = createPermission(
            "USER_MANAGEMENT",
            "Gestione Utenti",
            "Consente la gestione degli utenti del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );
    
    // Product Management Permission
    public static final Permission PRODUCT_MANAGEMENT = createPermission(
            "PRODUCT_MANAGEMENT",
            "Gestione Prodotti",
            "Consente la gestione dei prodotti del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );
    
    // Product Catalog Management Permission
    public static final Permission CATALOG_MANAGEMENT = createPermission(
            "CATALOG_MANAGEMENT",
            "Gestione Catalogo (Prodotti)",
            "Consente la visualizzazione del catalogo prodotti, punto di inizio per la creazione di quotazioni"
    );

    // Dealer Management Permission
    public static final Permission DEALER_MANAGEMENT = createPermission(
            "DEALER_MANAGEMENT",
            "Gestione Dealer",
            "Consente la gestione dei dealer del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Warranty Management Permission
    public static final Permission WARRANTY_MANAGEMENT = createPermission(
            "WARRANTY_MANAGEMENT",
            "Gestione Garanzie",
            "Consente la gestione delle garanzie, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Warranty Details Management Permission
    public static final Permission WARRANTY_DETAILS_MANAGEMENT = createPermission(
            "WARRANTY_DETAILS_MANAGEMENT",
            "Gestione Dettagli Garanzie",
            "Consente la gestione dei dettagli delle garanzie, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Insurance Company Management Permission
    public static final Permission INSURANCE_COMPANY_MANAGEMENT = createPermission(
            "INSURANCE_COMPANY_MANAGEMENT",
            "Gestione Compagnie Assicurative",
            "Consente la gestione delle compagnie assicurative, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Warranty Type Management Permission
    public static final Permission WARRANTY_TYPE_MANAGEMENT = createPermission(
            "WARRANTY_TYPE_MANAGEMENT",
            "Gestione Tipi di Garanzia",
            "Consente la gestione dei tipi di garanzia, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Insurance Provenance Type Management Permission
    public static final Permission INSURANCE_PROVENANCE_TYPE_MANAGEMENT = createPermission(
            "INSURANCE_PROVENANCE_TYPE_MANAGEMENT",
            "Gestione Tipi di Provenienza Assicurativa",
            "Consente la gestione dei tipi di provenienza assicurativa, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Channel Management Permission
    public static final Permission CHANNEL_MANAGEMENT = createPermission(
            "CHANNEL_MANAGEMENT",
            "Gestione Canali",
            "Consente la gestione dei canali, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Mail Template Management Permission
    public static final Permission MAIL_TEMPLATE_MANAGEMENT = createPermission(
            "MAIL_TEMPLATE_MANAGEMENT",
            "Gestione Modelli Email",
            "Consente la gestione dei modelli email, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // System Administration Permission
    public static final Permission SYSTEM_ADMINISTRATION = createPermission(
            "SYSTEM_ADMINISTRATION",
            "Amministrazione Sistema",
            "Consente l'accesso alle funzioni di amministrazione del sistema, incluse le impostazioni, la manutenzione e la configurazione del sistema"
    );

    // Contact Management Permission
    public static final Permission CONTACT_MANAGEMENT = createPermission(
            "CONTACT_MANAGEMENT",
            "Gestione Contatti",
            "Consente la gestione dei contatti del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Country Management Permission
    public static final Permission COUNTRY_MANAGEMENT = createPermission(
            "COUNTRY_MANAGEMENT",
            "Gestione Paesi",
            "Consente la gestione dei paesi del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Province Management Permission
    public static final Permission PROVINCE_MANAGEMENT = createPermission(
            "PROVINCE_MANAGEMENT",
            "Gestione Province",
            "Consente la gestione delle province del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // City Management Permission
    public static final Permission CITY_MANAGEMENT = createPermission(
            "CITY_MANAGEMENT",
            "Gestione Città",
            "Consente la gestione delle città del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Brand Management Permission
    public static final Permission BRAND_MANAGEMENT = createPermission(
            "BRAND_MANAGEMENT",
            "Gestione Marchi",
            "Consente la gestione dei marchi del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Model Management Permission
    public static final Permission MODEL_MANAGEMENT = createPermission(
            "MODEL_MANAGEMENT",
            "Gestione Modelli",
            "Consente la gestione dei modelli del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    // Permission Management Permission
    public static final Permission PERMISSION_MANAGEMENT = createPermission(
            "PERMISSION_MANAGEMENT",
            "Gestione Permessi",
            "Consente la gestione dei permessi di sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione"
    );

    /**
     * Helper method to create Permission objects
     */
    private static Permission createPermission(String code, String name, String description) {
        Permission permission = new Permission();
        permission.setCode(code);
        permission.setName(name);
        permission.setDescription(description);
        return permission;
    }

    /**
     * Get all defined permissions as an array
     * @return Array of all static Permission objects
     */
    public static Permission[] getAllPermissions() {
        return new Permission[] {
            USER_MANAGEMENT,
            DEALER_MANAGEMENT,
            WARRANTY_MANAGEMENT,
            WARRANTY_DETAILS_MANAGEMENT,
            INSURANCE_COMPANY_MANAGEMENT,
            WARRANTY_TYPE_MANAGEMENT,
            INSURANCE_PROVENANCE_TYPE_MANAGEMENT,
            CHANNEL_MANAGEMENT,
            MAIL_TEMPLATE_MANAGEMENT,
            CONTACT_MANAGEMENT,
            COUNTRY_MANAGEMENT,
            PROVINCE_MANAGEMENT,
            CITY_MANAGEMENT,
            BRAND_MANAGEMENT,
            MODEL_MANAGEMENT,
            SYSTEM_ADMINISTRATION,
            PERMISSION_MANAGEMENT
        };
    }
}
