/**
 * Common JavaScript Library for Tigani Application
 * Centralizes common initialization patterns for DataTables, forms, and components
 *
 * <AUTHOR> Augster
 * @version 1.0.0
 */

const TiganiLibs = (function() {
    'use strict';

    // Deep merge utility function
    function deepMerge(target, source) {
        const result = Object.assign({}, target);

        if (isObject(target) && isObject(source)) {
            Object.keys(source).forEach(key => {
                if (isObject(source[key])) {
                    if (!(key in target)) {
                        Object.assign(result, { [key]: source[key] });
                    } else {
                        result[key] = deepMerge(target[key], source[key]);
                    }
                } else {
                    Object.assign(result, { [key]: source[key] });
                }
            });
        }

        return result;
    }

    function isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }

    // Base DataTable Configuration Factory
    const DataTableFactory = {

        // Base configuration that's common across all DataTables
        getBaseConfig: function() {
            return {
                paging: true,
                pageLength: 10,
                searching: true,
                ordering: true,
                info: true,
                lengthChange: true,
                scrollCollapse: true,
                select: {
                    style: 'multi',
                    selector: 'td:select-checkbox'
                },
                responsive: {
                    details: {
                        type: 'column',
                        target: -1,
                    }
                },
                language: {
                    lengthMenu: 'Mostra _MENU_ elementi',
                    paginate: {
                        first: 'Primo',
                        last: 'Ultimo',
                        next: 'Successivo',
                        previous: 'Precedente'
                    },
                    info: 'Mostra da _START_ a _END_ di _TOTAL_ elementi',
                    infoEmpty: 'Mostra 0 a 0 di 0 elementi',
                    infoFiltered: '(filtrati da _MAX_ elementi totali)',
                    emptyTable: '<div class="p-5 h-full flex flex-col justify-center items-center text-center"><svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/><rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/><rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><g filter="url(#@@id)"><rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/><rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/><rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/><rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/><rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/></g><defs><filter id="@@id" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="6"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/></filter></defs></svg><div class="max-w-sm mx-auto"><p class="mt-2 text-sm text-gray-600 dark:text-neutral-400">Nessun dato disponibile</p></div></div>',
                    zeroRecords: '<div class="p-5 h-full flex flex-col justify-center items-center text-center"><svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/><rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/><rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><g filter="url(#@@id)"><rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/><rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/><rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/><rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/><rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/></g><defs><filter id="@@id" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="6"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/></filter></defs></svg><div class="max-w-sm mx-auto"><p class="mt-2 text-sm text-gray-600 dark:text-neutral-400">Nessun dato disponibile</p></div></div>',
                    loadingRecords: '<div class="p-5 h-full flex flex-col justify-center items-center text-center"><svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/><rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/><rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><g filter="url(#@@id)"><rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/><rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/><rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/><rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/><rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/></g><defs><filter id="@@id" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="6"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/></filter></defs></svg><div class="max-w-sm mx-auto"><p class="mt-2 text-sm text-gray-600 dark:text-neutral-400">Caricamento</p></div></div>',
                    processing: '<div class="p-5 h-full flex flex-col justify-center items-center text-center"><svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/><rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/><rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/><rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/><g filter="url(#@@id)"><rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/><rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/><rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/><rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/><rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/></g><defs><filter id="@@id" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="6"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/></filter></defs></svg><div class="max-w-sm mx-auto"><p class="mt-2 text-sm text-gray-600 dark:text-neutral-400">Elaborazione</p></div></div>'
                },
                pagingOptions: {
                    pageBtnClasses: 'min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700'
                },
                selecting: true,
                rowSelectingOptions: {
                    selectAllSelector: '#hs-table-search-checkbox-all'
                },
                layout: {
                    topStart: {
                        buttons: ["copy", "csv", "excel", "pdf", "print"]
                    }
                },
                order: [[1, 'asc']],
                columnDefs: [
                    {
                        targets: '_all',
                        className: 'p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200',
                    },
                    {
                        targets: 0,
                        orderable: false,
                        className: '!py-1 px-5 w-0 select-checkbox',
                        render: function(data, type, row, meta) {
                            if (type === 'display') {
                                const rowId = row.id || meta.row;
                                return `
                                    <div class="flex items-center h-5">
                                        <input id="hs-table-checkbox-${rowId}" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                        <label for="hs-table-checkbox-${rowId}" class="sr-only">Checkbox</label>
                                    </div>
                                `;
                            }
                            return data;
                        }
                    },
                    {
                        targets: -1,
                        orderable: false,
                        className: 'w-0 text-center',
                        render: function(data, type, row, meta) {
                            if (type === 'display') {
                                // This will be overridden by page-specific render functions
                                return data;
                            }
                            return data;
                        }
                    }
                ]
            };
        },

        // Factory method to create DataTable with merged configuration
        create: function(containerId, customOptions) {
            // Create a deep clone of the base configuration to avoid reference issues
            const baseConfig = this.getBaseConfig();
            const clonedBaseConfig = deepMerge({}, baseConfig);

            // Handle columnDefs merging specially - we need to merge arrays, not replace them
            let mergedConfig;
            if (customOptions && customOptions.columnDefs) {
                // Start with base config
                mergedConfig = deepMerge(clonedBaseConfig, customOptions);

                // Now properly merge columnDefs - combine base columnDefs with custom ones
                const baseColumnDefs = clonedBaseConfig.columnDefs || [];
                const customColumnDefs = customOptions.columnDefs || [];

                // Create a map to track which targets are overridden by custom columnDefs
                const customTargets = new Set();
                customColumnDefs.forEach(def => {
                    if (Array.isArray(def.targets)) {
                        def.targets.forEach(target => customTargets.add(target));
                    } else {
                        customTargets.add(def.targets);
                    }
                });

                // Filter base columnDefs to exclude those that are overridden
                const filteredBaseColumnDefs = baseColumnDefs.filter(def => {
                    if (Array.isArray(def.targets)) {
                        return !def.targets.some(target => customTargets.has(target));
                    } else {
                        return !customTargets.has(def.targets);
                    }
                });

                // Combine filtered base columnDefs with custom columnDefs
                mergedConfig.columnDefs = [...filteredBaseColumnDefs, ...customColumnDefs];
            } else {
                // No custom columnDefs, just merge normally
                mergedConfig = deepMerge(clonedBaseConfig, customOptions || {});
            }

            // Initialize HSDataTable with merged configuration
            return new HSDataTable(document.querySelector(containerId), mergedConfig);
        },

        // Common status renderers
        statusRenderers: {
            // Generic status renderer with color mapping
            renderStatus: function(status, colorMap) {
                const defaultColorMap = {
                    'ACTIVE': 'success',
                    'INACTIVE': 'secondary',
                    'PENDING': 'warning',
                    'DELETED': 'danger',
                    'ENABLED': 'success',
                    'DISABLED': 'secondary'
                };

                const colors = Object.assign({}, defaultColorMap, colorMap || {});
                const colorClass = colors[status] || 'secondary';

                return `<span class="badge bg-${colorClass}">${status}</span>`;
            },

            // User status renderer
            renderUserStatus: function(status) {
                const colorMap = {
                    'ACTIVE': 'success',
                    'INACTIVE': 'secondary',
                    'PENDING': 'warning'
                };
                return this.renderStatus(status, colorMap);
            },

            // Contact status renderer
            renderContactStatus: function(status) {
                const colorMap = {
                    'ACTIVE': 'success',
                    'INACTIVE': 'secondary'
                };
                return this.renderStatus(status, colorMap);
            },

            // Generic enabled/disabled renderer
            renderEnabledStatus: function(enabled) {
                return enabled ?
                    '<span class="badge bg-success">ENABLED</span>' :
                    '<span class="badge bg-secondary">DISABLED</span>';
            }
        }
    };

    // Form Validation Factory
    const FormValidationFactory = {

        // Base validation configuration
        getBaseConfig: function() {
            return {
                ignore: 'input[type=hidden], .select2-search__field',
                errorClass: 'validation-invalid-label text-xs text-red-600',
                successClass: 'validation-valid-label',
                validClass: 'validation-valid-label',
                errorPlacement: function(error, element) {
                    // Input with icons and Select2
                    if (element.hasClass('ckeditor')) {
                        error.appendTo(element.parent());
                    }
                    // Input with icons and Select2
                    else if (element.hasClass('select')) {
                        error.appendTo(element.parent());
                    }
                    // Input group, form checks and custom controls
                    else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                        error.appendTo(element.parent().parent());
                    }

                    // Other elements
                    else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element, errorClass, validClass) {
                    $(element).addClass(errorClass).removeClass(validClass);
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass(errorClass).addClass(validClass);
                },
                submitHandler: function(form) {
                    // Default submit handler - can be overridden
                    form.submit();
                }
            };
        },

        // Factory method to create form validation
        create: function(formSelector, customRules, customOptions) {
            const baseConfig = this.getBaseConfig();
            const config = deepMerge(baseConfig, customOptions || {});

            if (customRules) {
                config.rules = customRules;
            }

            return $(formSelector).validate(config);
        }
    };

    // Component Utilities
    const ComponentUtils = {

        // Initialize maxlength component
        initMaxlength: function(selector, options) {
            selector = selector || '.maxlength';
            const defaultOptions = {
                placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
                warningClass: 'bootstrap-maxlength text-muted form-text m-0',
                limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
            };
            const config = Object.assign({}, defaultOptions, options || {});
            $(selector).maxlength(config);
        },

        // Initialize permission checks for delete buttons
        initPermissionChecks: function() {
            if (typeof hasPermission === 'function') {
                if (!hasPermission('DELETE')) {
                    $('.btn-delete').hide();
                }
            }
        },

        // Initialize checkbox selection handlers
        initCheckboxHandlers: function(options) {
            const defaults = {
                selectAllSelector: '#hs-table-search-checkbox-all',
                rowCheckboxSelector: '.hs-table-search-checkbox',
                bulkActionSelector: '.bulk-action-btn'
            };

            const config = Object.assign({}, defaults, options || {});

            // Select all functionality
            $(document).on('change', config.selectAllSelector, function() {
                const isChecked = $(this).is(':checked');
                $(config.rowCheckboxSelector).prop('checked', isChecked);
                ComponentUtils.updateBulkActionButtons(config.bulkActionSelector);
            });

            // Individual checkbox functionality
            $(document).on('change', config.rowCheckboxSelector, function() {
                const totalCheckboxes = $(config.rowCheckboxSelector).length;
                const checkedCheckboxes = $(config.rowCheckboxSelector + ':checked').length;

                $(config.selectAllSelector).prop('checked', totalCheckboxes === checkedCheckboxes);
                ComponentUtils.updateBulkActionButtons(config.bulkActionSelector);
            });
        },

        // Update bulk action button visibility
        updateBulkActionButtons: function(selector) {
            const checkedCount = $('.hs-table-search-checkbox:checked').length;
            if (checkedCount > 0) {
                $(selector).show();
            } else {
                $(selector).hide();
            }
        },

        // Get selected row IDs
        getSelectedIds: function(checkboxSelector) {
            checkboxSelector = checkboxSelector || '.hs-table-search-checkbox:checked';
            return $(checkboxSelector).map(function() {
                return $(this).val();
            }).get();
        }
    };

    // UI Component Factories
    const UIComponentFactory = {

        // Select2 initialization
        initSelect2: function(options) {
            if (!$().select2) {
                console.warn('Warning - select2.min.js is not loaded.');
                return;
            }

            const defaults = {
                language: "it"
            };

            const config = Object.assign({}, defaults, options || {});

            // Default initialization
            $('.select').select2(config);

            // Icon format function for select-icons
            function iconFormat(icon) {
                if (!icon.id) {
                    return icon.text;
                }
                var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;
                return $icon;
            }

            // Initialize with icons if elements exist
            if ($('.select-icons').length > 0) {
                $('.select-icons').select2({
                    templateResult: iconFormat,
                    minimumResultsForSearch: Infinity,
                    templateSelection: iconFormat,
                    escapeMarkup: function (m) {
                        return m;
                    }
                });
            }
        },

        // CKEditor Classic initialization
        initCKEditor: function(selector, options) {
            if (typeof ClassicEditor === 'undefined') {
                console.warn('Warning - ckeditor_classic.js is not loaded.');
                return;
            }

            const defaults = {
                language: 'it',
                toolbar: {
                    // shouldNotGroupWhenFull: true
                },
                htmlSupport: {
                    allow: [
                        {
                            name: /.*/,
                            attributes: true,
                            classes: true,
                            styles: true
                        }
                    ]
                }
            };

            const config = deepMerge(defaults, options || {});
            const element = document.querySelector(selector || '#description');

            if (element) {
                return ClassicEditor.create(element, config);
            }
        },

        // DateRange Picker initialization
        initDateRange: function(selector, options) {
            if (!$().daterangepicker) {
                console.warn('Warning - daterangepicker.js is not loaded.');
                return;
            }

            const defaults = {
                locale: {
                    format: 'DD/MM/YYYY',
                    applyLabel: 'Applica',
                    cancelLabel: 'Annulla',
                    startLabel: 'Data inizio',
                    endLabel: 'Data fine',
                    customRangeLabel: 'Personalizzato',
                    daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                    monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                    firstDay: 1
                },
                drops: 'auto',
                parentEl: '.content-inner',
                singleDatePicker: true,
                autoApply: true
            };

            const config = deepMerge(defaults, options || {});
            const targetSelector = selector || '.daterange-single';

            $(targetSelector).each(function() {
                const startDate = $(this).val() || moment().format('DD/MM/YYYY');
                const elementConfig = Object.assign({}, config, { startDate: startDate });
                $(this).daterangepicker(elementConfig);
            });
        },

        // FilePond initialization
        initFilePond: function(selector, options) {
            if (typeof FilePond === 'undefined') {
                console.warn('Warning - FilePond is not loaded.');
                return;
            }

            // Register common plugins
            FilePond.registerPlugin(
                FilePondPluginImageExifOrientation,
                FilePondPluginImagePreview,
                FilePondPluginImageCrop,
                FilePondPluginImageResize,
                FilePondPluginImageFilter,
                FilePondPluginImageTransform,
                FilePondPluginImageEdit,
                FilePondPluginImageValidateSize,
                FilePondPluginFileEncode,
                FilePondPluginFileValidateType,
                FilePondPluginFileValidateSize
            );

            // Set Italian localization if available
            if (typeof FilePondIT !== 'undefined') {
                FilePond.setOptions(FilePondIT);
            }

            const inputElement = document.querySelector(selector || 'input[type="file"]');
            if (!inputElement) {
                return;
            }

            // Default configuration with Doka integration if available
            const defaults = {
                labelIdle: '<span class = "filepond--label-action cursor-pointer"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-up-icon lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"/><path d="m14 19.5 3-3 3 3"/><path d="M17 22v-5.5"/><circle cx="9" cy="9" r="2"/></svg><span>',
                allowFileEncode: true,
                allowImagePreview: true,
                allowImageTransform: true,
                allowImageEdit: true,
                allowImageCrop: true,
                stylePanelLayout: 'compact circle',
                stylePanelAspectRatio: '1:1',
                styleLoadIndicatorPosition: 'center bottom',
                styleProgressIndicatorPosition: 'right bottom',
                styleButtonRemoveItemPosition: 'center center',
                styleButtonProcessItemPosition: 'right bottom',
                imageCropAspectRatio: '1:1',
                imagePreviewHeight: 200,
                imageResizeTargetWidth: 400,
                imageResizeTargetHeight: 400,
                allowImageResize: true,
                allowImageExifOrientation: true,
                maxFileSize: '3MB',
                acceptedFileTypes: ['image/png', 'image/jpeg', 'image/svg']
            };

            if (typeof dokaCreate !== 'undefined') {
                const doka = dokaCreate({
                    //utils: 'crop, color',
                    cropAspectRatioOptions: [
                        {
                            label: 'Logo',
                            value: 1 / 1
                        }
                    ],
                    crop: {
                        aspectRatio: 1 / 1
                    },
                    labelButtonReset: "Reimposta",
                    labelButtonCancel: "Annulla",
                    labelButtonConfirm: "Conferma",
                    labelButtonUtilCrop: "Ritaglia",
                    labelButtonUtilResize: "Ridimensiona",
                    labelButtonUtilFilter: "Filtra",
                    labelButtonUtilColor: "Colori",
                    labelButtonUtilMarkup: "Annota",
                    labelStatusMissingWebGL: "WebGL è richiesto ma è disabilitato nel tuo browser",
                    labelStatusAwaitingImage: "In attesa dell'immagine…",
                    labelStatusLoadImageError: "Errore nel caricamento dell'immagine…",
                    labelStatusLoadingImage: "Caricamento dell'immagine…",
                    labelStatusProcessingImage: "Elaborazione dell'immagine…",
                    labelColorBrightness: "Luminosità",
                    labelColorContrast: "Contrasto",
                    labelColorExposure: "Esposizione",
                    labelColorSaturation: "Saturazione",
                    labelMarkupTypeRectangle: "Rettangolo",
                    labelMarkupTypeEllipse: "Cerchio",
                    labelMarkupTypeText: "Testo",
                    labelMarkupTypeLine: "Linea",
                    labelMarkupSelectFontSize: "Dimensione",
                    labelMarkupSelectFontFamily: "Carattere",
                    labelMarkupSelectLineDecoration: "Decorazione",
                    labelMarkupSelectLineStyle: "Stile",
                    labelMarkupSelectShapeStyle: "Stile",
                    labelMarkupRemoveShape: "Rimuovi",
                    labelMarkupToolSelect: "Seleziona",
                    labelMarkupToolDraw: "Disegna",
                    labelMarkupToolLine: "Linea",
                    labelMarkupToolText: "Testo",
                    labelMarkupToolRect: "Rettangolo",
                    labelMarkupToolEllipse: "Cerchio",
                    labelResizeWidth: "Larghezza",
                    labelResizeHeight: "Altezza",
                    labelResizeApplyChanges: "Applica modifiche",
                    labelCropInstructionZoom: "Zoom avanti e indietro con la rotellina del mouse o il touchpad.",
                    labelButtonCropZoom: "Zoom",
                    labelButtonCropRotateLeft: "Ruota a sinistra",
                    labelButtonCropRotateRight: "Ruota a destra",
                    labelButtonCropRotateCenter: "Centra rotazione",
                    labelButtonCropFlipHorizontal: "Rifletti orizzontalmente",
                    labelButtonCropFlipVertical: "Rifletti verticalmente",
                    labelButtonCropAspectRatio: "Proporzioni",
                    labelButtonCropToggleLimit: "Selezione ritaglio",
                    labelButtonCropToggleLimitEnable: "Limitato all'immagine",
                    labelButtonCropToggleLimitDisable: "Seleziona fuori immagine",
                    pointerEventsPolyfillScope: "ambito",
                    styleCropCorner: "angolo",
                    styleFullscreenSafeArea: "area sicura a schermo intero"
                });

                defaults.imageEditEditor = doka;
            }

            const config = deepMerge(defaults, options || {});
            return FilePond.create(inputElement, config);
        }
    };

    // Action Handlers Factory
    const ActionHandlerFactory = {

        // Generic delete button handler
        initDeleteButton: function(options) {
            const defaults = {
                buttonSelector: '[id*="delete-"][id*="-btn"]',
                permissionCheck: null, // e.g., 'USER_MANAGEMENT'
                permissionType: 'delete',
                entityIdAttribute: null, // e.g., 'data-userid'
                confirmTitle: 'Sei sicuro?',
                confirmText: 'Questa azione non può essere annullata.',
                confirmButtonText: 'Sì, elimina',
                cancelButtonText: 'Annulla',
                onDelete: null // Custom delete handler function
            };

            const config = Object.assign({}, defaults, options || {});

            $(document).on('click', config.buttonSelector, function(e) {
                e.preventDefault();

                // Permission check
                if (config.permissionCheck && typeof hasPermission === 'function') {
                    if (!hasPermission(config.permissionCheck, config.permissionType)) {
                        if (typeof showToast === 'function') {
                            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError('Non hai i permessi per eseguire questa operazione.');
                        }
                        return;
                    }
                }

                // Get entity ID
                let entityId = null;
                if (config.entityIdAttribute) {
                    entityId = $(this).data(config.entityIdAttribute.replace('data-', ''));
                    if (!entityId) {
                        if (typeof showToast === 'function') {
                            showToast('Errore: ID entità non trovato', 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError('Errore: ID entità non trovato');
                        }
                        return;
                    }
                }

                // Show confirmation dialog - prefer jQuery Confirm if available, fallback to SweetAlert
                if (typeof $.confirm !== 'undefined') {
                    $.confirm({
                        title: config.confirmTitle,
                        content: config.confirmText,
                        type: 'red',
                        typeAnimated: true,
                        buttons: {
                            elimina: {
                                text: config.confirmButtonText,
                                btnClass: 'btn-red',
                                action: function () {
                                    if (typeof config.onDelete === 'function') {
                                        config.onDelete(entityId, $(this));
                                    }
                                }
                            },
                            annulla: {
                                text: config.cancelButtonText,
                                btnClass: 'btn-light'
                            }
                        }
                    });
                } else if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: config.confirmTitle,
                        text: config.confirmText,
                        icon: 'warning',
                        showCancelButton: true,
                        reverseButtons: true,
                        customClass: {
                            cancelButton: "btn btn-light",
                            confirmButton: "btn btn-danger"
                        },
                        buttonsStyling: false,
                        cancelButtonText: config.cancelButtonText,
                        confirmButtonText: config.confirmButtonText
                    }).then((result) => {
                        if (result.isConfirmed && typeof config.onDelete === 'function') {
                            config.onDelete(entityId, $(this));
                        }
                    });
                }
            });
        },

        // Generic permission-based element visibility
        initPermissionBasedVisibility: function(permissions) {
            if (typeof hasPermission !== 'function') {
                return;
            }

            // Hide elements based on permissions
            Object.keys(permissions).forEach(selector => {
                const permission = permissions[selector];
                if (!hasPermission(permission.code, permission.type)) {
                    $(selector).hide();
                }
            });
        }
    };

    // Notification Utilities
    const NotificationUtils = {

        // Show info notification
        showInfo: function(message) {
            showToast(message, 'info');
        },

        // Show success notification
        showSuccess: function(message) {
            showToast(message, 'success');
        },

        // Show error notification
        showError: function(message) {
            showToast(message, 'error');
        },

        // Show warning notification
        showWarning: function(message) {
            showToast(message, 'warning');
        }
    };

    /**
     * Tax Code Calculator Utility
     * Provides functionality to calculate Italian tax code (codice fiscale) from form fields
     */
    const TaxCodeCalculator = {
        /**
         * Calculate tax code from form fields
         * @param {Object} options - Configuration options
         * @param {string} options.firstNameField - ID of first name field (default: 'firstName')
         * @param {string} options.lastNameField - ID of last name field (default: 'lastName')
         * @param {string} options.genderField - ID of gender field (default: 'gender')
         * @param {string} options.birthDateField - ID of birth date field (default: 'birthDate')
         * @param {string} options.birthCityField - ID of birth city field (default: 'birthCity')
         * @param {string} options.targetField - ID of target field to populate with result (default: 'tin')
         * @param {function} options.onSuccess - Success callback function
         * @param {function} options.onError - Error callback function
         */
        calculate: function(options) {
            const defaults = {
                firstNameField: 'firstName',
                lastNameField: 'lastName',
                genderField: 'gender',
                birthDateField: 'birthDate',
                birthCityField: 'birthCity',
                targetField: 'tin',
                onSuccess: null,
                onError: null
            };

            const config = Object.assign({}, defaults, options || {});

            // Collect form data
            const formData = this.collectFormData(config);

            // Validate required fields
            const validation = this.validateFormData(formData);
            if (!validation.isValid) {
                const errorMessage = 'Campi mancanti per il calcolo del codice fiscale: ' + validation.missingFields.join(', ');
                TiganiLibs.NotificationUtils.showError(errorMessage);
                if (config.onError) {
                    config.onError(errorMessage, validation.missingFields);
                }
                return;
            }

            // Show loading notification
            TiganiLibs.NotificationUtils.showInfo('Calcolo del codice fiscale in corso...');

            // Make AJAX call to calculate tax code
            $.ajax({
                url: appRoutes.get('BE_DATA_CALCULATE_TIN'),
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.taxCode) {
                        // Populate target field
                        const targetElement = document.getElementById(config.targetField);
                        if (targetElement) {
                            targetElement.value = response.taxCode;
                            // Trigger change event for any listeners
                            $(targetElement).trigger('change');
                        }

                        // Show success notification
                        TiganiLibs.NotificationUtils.showSuccess(response.message || 'Codice fiscale calcolato con successo');

                        // Call success callback
                        if (config.onSuccess) {
                            config.onSuccess(response.taxCode, response);
                        }
                    } else {
                        const errorMessage = response.error || 'Errore nel calcolo del codice fiscale';
                        TiganiLibs.NotificationUtils.showError(errorMessage);
                        if (config.onError) {
                            config.onError(errorMessage, response);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'Errore nella comunicazione con il server';

                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            errorMessage = response.error;
                        }
                        if (response.missingFields) {
                            if (config.onError) {
                                config.onError(errorMessage, response.missingFields);
                            }
                        }
                    } catch (e) {
                        // Response is not JSON, use generic error
                    }

                    TiganiLibs.NotificationUtils.showError(errorMessage);
                    if (config.onError) {
                        config.onError(errorMessage, null);
                    }
                }
            });
        },

        /**
         * Collect form data from specified fields
         * @param {Object} config - Field configuration
         * @returns {Object} Form data object
         */
        collectFormData: function(config) {
            const data = {};

            // Get field values
            const firstNameEl = document.getElementById(config.firstNameField);
            const lastNameEl = document.getElementById(config.lastNameField);
            const genderEl = document.getElementById(config.genderField) || document.querySelector(`input[name="${config.genderField}"]:checked`);
            const birthDateEl = document.getElementById(config.birthDateField);
            const birthCityEl = document.getElementById(config.birthCityField);

            if (firstNameEl) data.firstName = firstNameEl.value.trim();
            if (lastNameEl) data.lastName = lastNameEl.value.trim();
            if (genderEl) data.gender = genderEl.value.trim();
            if (birthDateEl) data.birthDate = birthDateEl.value.trim();
            if (birthCityEl) {
                // Handle both input and select elements
                if (birthCityEl.tagName.toLowerCase() === 'select') {
                    data.birthCity = $("#" + config.birthCityField).val().trim();
                } else {
                    data.birthCity = birthCityEl.value.trim();
                }
            }

            return data;
        },

        /**
         * Validate form data for required fields
         * @param {Object} formData - Form data to validate
         * @returns {Object} Validation result with isValid flag and missingFields array
         */
        validateFormData: function(formData) {
            const missingFields = [];
            const fieldLabels = {
                firstName: 'Nome',
                lastName: 'Cognome',
                gender: 'Genere',
                birthDate: 'Data di nascita',
                birthCity: 'Città di nascita'
            };

            // Check required fields
            Object.keys(fieldLabels).forEach(function(field) {
                if (!formData[field] || formData[field].length === 0) {
                    missingFields.push(fieldLabels[field]);
                }
            });

            return {
                isValid: missingFields.length === 0,
                missingFields: missingFields
            };
        },

        /**
         * Initialize tax code calculation link for a form
         * @param {string} linkSelector - CSS selector for the calculation link
         * @param {Object} options - Configuration options (same as calculate method)
         */
        initCalculationLink: function(linkSelector, options) {
            $(document).on('click', linkSelector, function(e) {
                e.preventDefault();
                TaxCodeCalculator.calculate(options);
            });
        }
    };

    // Public API
    return {
        DataTableFactory: DataTableFactory,
        FormValidationFactory: FormValidationFactory,
        ComponentUtils: ComponentUtils,
        UIComponentFactory: UIComponentFactory,
        ActionHandlerFactory: ActionHandlerFactory,
        NotificationUtils: NotificationUtils,
        TaxCodeCalculator: TaxCodeCalculator,
        deepMerge: deepMerge
    };

})();

// Make TiganiLibs globally available
window.TiganiLibs = TiganiLibs;

$(document).ready(function() {
    console.log('TiganiLibs loaded successfully');
    HSStaticMethods.autoInit();
});
